const express = require('express');
const router = express.Router();
const { Session } = require('../models/tarot');
const { User } = require('../models/User');
const { authenticateToken, optionalAuthenticateToken } = require('../middleware/auth');
const { getConnection } = require('../services/database');

// Create a new session
router.post('/', optionalAuthenticateToken, async (req, res) => {
  try {
    // console.log('=== Create session start ===');
    // console.log('Request body:', req.body);

    const question = req.body.question;
    const { fingerprint } = req.body;
    
    if (!question) {
      return res.status(400).json({
        success: false,
        message: 'Question is required'
      });
    }

    // 处理匿名用户和登录用户
    if (!req.user) {
      // 匿名用户逻辑
      if (!fingerprint) {
        return res.status(400).json({
          success: false,
          message: '匿名用户需要提供浏览器指纹'
        });
      }

      // 检查匿名用户是否已使用过免费机会
      const pool = await getConnection();
      const [existingRecords] = await pool.query(
        'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
        [fingerprint]
      );

      if (existingRecords[0].count > 0) {
        return res.status(403).json({
          success: false,
          message: '您的免费占卜次数已用完，请登录获取更多次数',
          errorCode: 'ANONYMOUS_LIMIT_EXCEEDED'
        });
      }

      // 为匿名用户创建临时会话（不存储到数据库）
      const tempSessionId = require('uuid').v4();
      return res.json({
        success: true,
        session: {
          id: tempSessionId,
          question: question,
          status: 'created',
          isAnonymous: true
        }
      });
    }

    // 登录用户逻辑
    const userId = req.user.userId;

    // 准备会话数据
    const sessionData = {
      user_id: userId,
      question: question,
      status: req.body.status || 'created'
    };

    // 如果有读者信息，添加到会话数据中
    if (req.body.selectedReader) {
      sessionData.reader_id = req.body.selectedReader.id;
      sessionData.reader_name = req.body.selectedReader.name;
      sessionData.reader_type = req.body.selectedReader.type || 'basic';
    } else if (req.body.readerId) {
      // 兼容直接传递readerId的情况
      sessionData.reader_id = req.body.readerId;
      sessionData.reader_name = req.body.readerId === 'basic' ? '茉伊' : req.body.readerId;
      sessionData.reader_type = 'basic';
    }

    // 如果有卡牌信息，添加到会话数据中
    if (req.body.selectedCards) {
      sessionData.selected_cards = req.body.selectedCards;
    }

    // 如果有位置信息，添加到会话数据中
    if (req.body.selectedPositions) {
      sessionData.selected_positions = req.body.selectedPositions;
    }

    // 移除用户星座信息的数据库存储
    // 星座信息只在前端localStorage中保存，不存储到数据库
    // if (req.body.userZodiacInfo) {
    //   sessionData.user_data = req.body.userZodiacInfo;
    // }

    // 如果有牌阵信息，添加到会话数据中
    if (req.body.selectedSpread) {
      sessionData.spread_id = req.body.selectedSpread.id || 'daily-fortune';
      sessionData.spread_name = req.body.selectedSpread.name || '每日运势';
      sessionData.spread_card_count = req.body.selectedSpread.cardCount || 1;

      console.log(`[Session] 设置牌阵信息:`, {
        spread_id: sessionData.spread_id,
        spread_name: sessionData.spread_name,
        spread_card_count: sessionData.spread_card_count,
        source: 'selectedSpread'
      });
    } else if (req.body.spreadId) {
      // 如果直接提供了 spreadId，使用它
      sessionData.spread_id = req.body.spreadId;

      console.log(`[Session] 设置牌阵ID:`, {
        spread_id: sessionData.spread_id,
        source: 'spreadId'
      });
    } else {
      console.log(`[Session] 未设置牌阵信息，spread_id将为NULL`);
    }

    // 检查用户是否有足够的剩余次数
    const user = await User.findById(req.user.userId);
    if (!user) {
      // console.log('User not found');
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    // console.log('User found:', { ...user, password: '[REDACTED]' });

    // 如果是每日运势请求，检查今天是否已经有会话
    const pool = await getConnection();
    if (req.body.selectedSpread && (req.body.selectedSpread.id === 'daily-fortune' || 
        (typeof req.body.selectedSpread === 'string' && req.body.selectedSpread.includes('daily')))) {
      
      // 获取今天的日期范围
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
      const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
      
      // 查询数据库，检查用户今天是否已经有每日运势会话
      const [existingSessions] = await pool.query(`
        SELECT id, status, fortune_result, selected_cards
        FROM sessions
        WHERE user_id = ?
        AND spread_id = 'daily-fortune'
        AND timestamp BETWEEN ? AND ?
        ORDER BY timestamp DESC
        LIMIT 1
      `, [user.id, todayStart, todayEnd]);
      
      // 如果已经存在会话，返回该会话而不是创建新会话
      if (existingSessions.length > 0) {
        const existingSession = existingSessions[0];
        console.log(`用户今天已有每日运势会话，返回现有会话: ${existingSession.id}`);
        
        // 如果请求中包含selectedCards但现有会话没有，可以更新现有会话
        if (req.body.selectedCards && 
            (!existingSession.selected_cards || 
            existingSession.selected_cards === '[]' || 
            existingSession.selected_cards === 'null')) {
          
          await pool.query(
            'UPDATE sessions SET selected_cards = ?, status = ? WHERE id = ?',
            [JSON.stringify(req.body.selectedCards), 'cards_selected', existingSession.id]
          );
          
          existingSession.selected_cards = JSON.stringify(req.body.selectedCards);
          existingSession.status = 'cards_selected';
        }
        
        return res.status(200).json({
          success: true,
          session: {
            id: existingSession.id,
            userId: user.id,
            question: existingSession.question || req.body.question,
            status: existingSession.status || 'pending',
            selectedCards: req.body.selectedCards || [],
            selectedReader: null,
            selectedSpread: req.body.selectedSpread || null,
            readingResult: existingSession.fortune_result ? JSON.parse(existingSession.fortune_result) : null
          },
          message: '使用已存在的每日运势会话'
        });
      }
    } else if (req.body.selectedSpread && req.body.selectedSpread.id === 'yearly-fortune') {
      // 对于年运占卜，检查是否有未完成的会话
      const [existingSessions] = await pool.query(`
        SELECT id, status, fortune_result, selected_cards
        FROM sessions
        WHERE user_id = ?
        AND spread_id = 'yearly-fortune'
        AND status != 'completed'
        ORDER BY timestamp DESC
        LIMIT 1
      `, [user.id]);

      // 如果存在未完成的会话，返回该会话
      if (existingSessions.length > 0) {
        const existingSession = existingSessions[0];
        console.log(`用户有未完成的年运占卜会话，返回现有会话: ${existingSession.id}`);
        
        // 如果请求中包含selectedCards但现有会话没有，更新现有会话
        if (req.body.selectedCards && 
            (!existingSession.selected_cards || 
            existingSession.selected_cards === '[]' || 
            existingSession.selected_cards === 'null')) {
          
          await pool.query(
            'UPDATE sessions SET selected_cards = ?, status = ? WHERE id = ?',
            [JSON.stringify(req.body.selectedCards), 'cards_selected', existingSession.id]
          );
          
          existingSession.selected_cards = JSON.stringify(req.body.selectedCards);
          existingSession.status = 'cards_selected';
        }
        
        return res.status(200).json({
          success: true,
          session: {
            id: existingSession.id,
            userId: user.id,
            question: existingSession.question || req.body.question,
            status: existingSession.status || 'pending',
            selectedCards: req.body.selectedCards || [],
            selectedReader: null,
            selectedSpread: req.body.selectedSpread || null,
            readingResult: existingSession.fortune_result ? JSON.parse(existingSession.fortune_result) : null
          },
          message: '使用已存在的年运占卜会话'
        });
      }
    }

    // 获取用户所有占卜次数，排除每日运势（daily-fortune）的会话
    const [allSessions] = await pool.query(
      'SELECT COUNT(*) as count FROM sessions WHERE user_id = ? AND (spread_id != "daily-fortune" OR spread_id IS NULL)',
      [user.id]
    );
    const sessionCount = allSessions[0].count;
    // console.log('Session count (excluding daily fortune):', sessionCount);
    // console.log('User remaining_reads from DB:', user.remaining_reads);

    // 判断是否是每日运势请求
    const isDailyFortune = req.body.selectedSpread && 
                          (req.body.selectedSpread.id === 'daily-fortune' || 
                          (typeof req.body.selectedSpread === 'string' && 
                          req.body.selectedSpread.includes('daily')));
    
    // 只有在非每日运势请求时才检查剩余次数和VIP状态
    if (!isDailyFortune) {
      // 计算实际剩余次数
      const actualRemainingReads = Math.max(0, user.remaining_reads - sessionCount);
      // console.log('Calculated actualRemainingReads:', actualRemainingReads);
  
      if (actualRemainingReads <= 0 && user.vip_status === 'none') {
        // console.log('No remaining reads and not VIP, rejecting request');
        return res.status(400).json({
          success: false,
          message: '您的免费占卜次数已用完，请升级到VIP继续使用'
        });
      }
    }

    // console.log('Prepared session data:', sessionData);

    const session = await Session.create(sessionData);
    // console.log('Created session:', session);
    
    const response = {
      success: true,
      session: {
        id: session.id,
        userId: session.user_id,
        question: session.question,
        status: session.status,
        selectedCards: req.body.selectedCards || [],
        selectedReader: null,
        selectedSpread: req.body.selectedSpread || null,
        readingResult: null
      }
    };
    // console.log('Sending response:', response);
    // console.log('=== Create session end ===');
    
    res.status(201).json(response);
  } catch (error) {
    console.error('Error creating session:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Failed to create session',
      error: error.message
    });
  }
});

// Get active session
router.get('/active', authenticateToken, async (req, res) => {
  try {
    const session = await Session.findActiveByUserId(req.user.userId);

    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'No active session found'
      });
    }

    res.json({
      success: true,
      session: {
        id: session.id,
        userId: session.user_id,
        question: session.question,
        status: session.status,
        selectedCards: session.selectedCards || [],
        selectedReader: session.selectedReader,
        selectedSpread: session.selectedSpread,
        readingResult: session.readingResult
      }
    });
  } catch (error) {
    console.error('Error fetching active session:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active session',
      error: error.message
    });
  }
});

// Get user's session history
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const sessions = await Session.findByUserId(req.user.userId);

    res.json({
      success: true,
      sessions: sessions.map(session => ({
        id: session.id,
        userId: session.user_id,
        question: session.question,
        status: session.status,
        selectedCards: session.selectedCards || [],
        selectedReader: session.selectedReader,
        selectedSpread: session.selectedSpread,
        readingResult: session.readingResult,
        timestamp: session.timestamp
      }))
    });
  } catch (error) {
    console.error('Error fetching session history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch session history',
      error: error.message
    });
  }
});

// Get session by ID
router.get('/:sessionId', authenticateToken, async (req, res) => {
  try {
    const pool = await getConnection();
    const [sessions] = await pool.query(
      `SELECT 
        id, 
        user_id, 
        question, 
        status, 
        selected_cards,
        reader_id,
        reader_name,
        reader_type,
        spread_id,
        spread_name,
        spread_card_count,
        reading_result,
        timestamp,
        deep_analysis,
        dialog_history
      FROM sessions WHERE id = ?`,
      [req.params.sessionId]
    );

    const session = sessions[0];

    if (!session || session.user_id !== req.user.userId) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    res.json({
      success: true,
      session: {
        id: session.id,
        userId: session.user_id,
        question: session.question,
        status: session.status,
        selectedCards: typeof session.selected_cards === 'string' 
          ? JSON.parse(session.selected_cards) 
          : session.selected_cards || [],
        selectedReader: session.reader_id ? {
          id: session.reader_id,
          name: session.reader_name,
          type: session.reader_type,
          nameEn: session.reader_id === 'basic' ? 'Molly' : 
                 session.reader_id === 'elias' ? 'Elias' :
                 session.reader_id === 'claire' ? 'Claire' :
                 session.reader_id === 'raven' ? 'Raven' : 
                 session.reader_id === 'aurora' ? 'Aurora' :
                 session.reader_id === 'vincent' ? 'Vincent' :
                 'Molly'
        } : null,
        selectedSpread: session.spread_id ? {
          id: session.spread_id,
          name: session.spread_name,
          cardCount: session.spread_card_count
        } : null,
        readingResult: typeof session.reading_result === 'string'
          ? JSON.parse(session.reading_result)
          : session.reading_result,
        timestamp: session.timestamp,
        deepAnalysis: session.deep_analysis,
        dialogHistory: typeof session.dialog_history === 'string'
          ? JSON.parse(session.dialog_history)
          : session.dialog_history || []
      }
    });
  } catch (error) {
    console.error('Error fetching session:', error);
    console.error('Error details:', error.message);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch session',
      error: error.message
    });
  }
});

// Update session
router.patch('/:sessionId', optionalAuthenticateToken, async (req, res) => {
  try {
    const sessionId = req.params.sessionId;

    // 对于匿名用户，直接返回成功（不实际更新数据库）
    if (!req.user) {
      return res.json({
        success: true,
        session: {
          id: sessionId,
          status: req.body.status || 'updated',
          isAnonymous: true
        }
      });
    }

    // 登录用户的会话更新逻辑
    const session = await Session.findById(sessionId);

    if (!session || session.user_id !== req.user.userId) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    // Update session data
    const updatedSession = await Session.update(sessionId, req.body);

    res.json({
      success: true,
      session: {
        id: updatedSession.id,
        userId: updatedSession.user_id,
        question: updatedSession.question,
        status: updatedSession.status,
        selectedCards: updatedSession.selectedCards || [],
        selectedReader: updatedSession.selectedReader,
        selectedSpread: updatedSession.selectedSpread,
        readingResult: updatedSession.readingResult,
        timestamp: updatedSession.timestamp
      }
    });
  } catch (error) {
    console.error('Error updating session:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update session',
      error: error.message
    });
  }
});

// Get session advice
router.get('/:sessionId/advice', authenticateToken, async (req, res) => {
  try {
    const pool = await getConnection();
    const [sessions] = await pool.query(
      'SELECT advice FROM sessions WHERE id = ? AND user_id = ?',
      [req.params.sessionId, req.user.userId]
    );

    if (sessions.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    let advice = null;
    try {
      if (sessions[0].advice) {
        advice = JSON.parse(sessions[0].advice);
      }
    } catch (error) {
      console.error('Error parsing advice:', error);
    }

    res.json({
      success: true,
      advice
    });
  } catch (error) {
    console.error('Error fetching session advice:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch session advice',
      error: error.message
    });
  }
});

// 获取用户最新的每日运势结果
router.get('/daily-fortune/latest', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 获取今天的日期范围
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    // 查询数据库，获取用户今天最新的每日运势结果
    const pool = await getConnection();
    const [sessions] = await pool.query(`
      SELECT 
        s.*,
        s.selected_cards as cards_json,
        s.fortune_result
      FROM sessions s
      WHERE s.user_id = ?
      AND s.spread_id = 'daily-fortune'
      AND s.timestamp BETWEEN ? AND ?
      ORDER BY s.timestamp DESC
      LIMIT 1
    `, [userId, todayStart, todayEnd]);
    
    if (sessions && sessions.length > 0) {
      res.json({
        success: true,
        session: sessions[0]
      });
    } else {
      res.json({
        success: false,
        message: '未找到今日运势结果'
      });
    }
  } catch (error) {
    console.error('Error getting latest daily fortune:', error);
    res.status(500).json({ 
      success: false,
      error: '获取最新每日运势结果时出错' 
    });
  }
});

// 添加检查会话是否存在的路由
router.get('/check', authenticateToken, async (req, res) => {
  try {
    const { spreadId, status } = req.query;
    const userId = req.user.userId;
    
    if (!spreadId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }
    
    const pool = await getConnection();
    
    // 构建查询条件
    let query = `
      SELECT id, status, fortune_result, selected_cards, question
      FROM sessions
      WHERE user_id = ?
      AND spread_id = ?
    `;
    
    const queryParams = [userId, spreadId];
    
    // 如果提供了状态，添加状态条件
    if (status) {
      query += ` AND status = ?`;
      queryParams.push(status);
    } else {
      // 默认查找未完成的会话
      query += ` AND status != 'completed'`;
    }
    
    query += ` ORDER BY timestamp DESC LIMIT 1`;
    
    const [sessions] = await pool.query(query, queryParams);
    
    if (sessions.length > 0) {
      const session = sessions[0];
      return res.status(200).json({
        success: true,
        session: {
          id: session.id,
          userId,
          status: session.status,
          question: session.question,
          selectedCards: session.selected_cards ? JSON.parse(session.selected_cards) : [],
          readingResult: session.fortune_result ? JSON.parse(session.fortune_result) : null
        },
        message: '找到现有会话'
      });
    }
    
    return res.status(200).json({
      success: true,
      session: null,
      message: '未找到现有会话'
    });
  } catch (error) {
    console.error('检查会话失败:', error);
    res.status(500).json({
      success: false,
      message: '检查会话失败',
      error: error.message
    });
  }
});

// 获取会话类型
router.get('/types/:sessionId', optionalAuthenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    // 获取数据库连接
    const pool = await getConnection();
    
    // 获取会话信息 - 使用实际存在的字段
    const [sessionRows] = await pool.query(
      `SELECT id, deep_analysis, dialog_history, spread_id FROM sessions WHERE id = ?`,
      [sessionId]
    );
    
    if (sessionRows.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到会话' 
      });
    }
    
    const session = sessionRows[0];
    
    // 根据实际字段判断会话类型
    // deep_analysis字段非空表示有深度解析
    // dialog_history非空表示有追问记录
    res.json({
      success: true,
      spreadId: session.spread_id, // 返回spread_id用于确定页面类型
      types: {
        basicReading: true, // 基础解读总是存在
        deepAnalysis: Boolean(session.deep_analysis), // 有deep_analysis字段就表示有深度解析
        followup: Boolean(session.dialog_history) // 有dialog_history字段就表示有追问
      }
    });
  } catch (error) {
    console.error('获取会话类型失败:', error);
    res.status(500).json({ success: false, message: '获取会话类型失败，请稍后重试' });
  }
});

module.exports = router;
